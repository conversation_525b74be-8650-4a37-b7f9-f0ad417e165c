import { UserType } from './auth';

export interface UserProfile {
  id: string;
  email: string;
  fullName: string;
  phone: string;
  userType: UserType;
  profilePicture?: string;
  dateOfBirth?: string;
  cpf?: string;
  rg?: string;
  address?: Address;
  documents: UserDocument[];
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isDocumentVerified: boolean;
  verificationStatus: VerificationStatus;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  preferences: UserPreferences;
  statistics: UserStatistics;
}

export interface Address {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface UserDocument {
  id: string;
  type: DocumentType;
  fileName: string;
  fileUrl: string;
  uploadedAt: string;
  verificationStatus: DocumentVerificationStatus;
  verifiedAt?: string;
  rejectionReason?: string;
}

export type DocumentType = 
  | 'cpf' 
  | 'rg' 
  | 'comprovante_residencia' 
  | 'comprovante_renda' 
  | 'foto_perfil';

export type DocumentVerificationStatus = 
  | 'pending' 
  | 'approved' 
  | 'rejected' 
  | 'expired';

export type VerificationStatus = 
  | 'unverified' 
  | 'pending' 
  | 'partial' 
  | 'verified' 
  | 'rejected';

export interface UserPreferences {
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    showProfile: boolean;
    showContact: boolean;
  };
  biometric: {
    enabled: boolean;
    type?: 'fingerprint' | 'faceId' | 'iris';
  };
}

export interface UserStatistics {
  totalProperties: number;
  totalRentals: number;
  totalViews: number;
  averageRating: number;
  totalReviews: number;
  joinedDate: string;
}

export interface UpdateProfileData {
  fullName?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: Partial<Address>;
  preferences?: Partial<UserPreferences>;
}

export interface DocumentUploadData {
  type: DocumentType;
  file: {
    uri: string;
    name: string;
    type: string;
  };
}
