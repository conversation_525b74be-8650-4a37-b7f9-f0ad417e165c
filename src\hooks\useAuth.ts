import { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

/**
 * Hook para usar o contexto de autenticação
 * 
 * @returns Contexto de autenticação com estado e métodos
 * @throws Error se usado fora do AuthProvider
 */
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  
  return context;
}

/**
 * Hook para verificar se o usuário está autenticado
 * 
 * @returns boolean indicando se o usuário está autenticado
 */
export function useIsAuthenticated(): boolean {
  const { isAuthenticated } = useAuth();
  return isAuthenticated;
}

/**
 * Hook para obter dados do usuário atual
 * 
 * @returns Dados do usuário ou null se não autenticado
 */
export function useCurrentUser() {
  const { user } = useAuth();
  return user;
}

/**
 * Hook para verificar se está carregando
 * 
 * @returns boolean indicando se está em estado de loading
 */
export function useAuthLoading(): boolean {
  const { isLoading } = useAuth();
  return isLoading;
}

/**
 * Hook para obter erro de autenticação
 * 
 * @returns String com erro ou null se não há erro
 */
export function useAuthError(): string | null {
  const { error } = useAuth();
  return error;
}
