import React from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

import { RegisterForm } from '../../components/forms/RegisterForm';
import { colors } from '../../styles/colors';

export default function RegisterScreen() {
  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleDocumentVerification = () => {
    router.push('/(auth)/document-verification');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <RegisterForm
            onLogin={handleLogin}
            onDocumentVerification={handleDocumentVerification}
          />
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  keyboardView: {
    flex: 1,
  },

  content: {
    flex: 1,
  },
});
