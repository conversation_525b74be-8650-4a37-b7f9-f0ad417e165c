import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { AuthUser } from '../types/auth';

// Chaves para armazenamento
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  BIOMETRIC_ENABLED: 'biometric_enabled',
  REMEMBER_ME: 'remember_me',
  LAST_EMAIL: 'last_email',
  APP_PREFERENCES: 'app_preferences',
} as const;

/**
 * Armazena token de acesso de forma segura
 */
export async function storeAccessToken(token: string): Promise<void> {
  try {
    await SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, token);
  } catch (error) {
    console.error('Erro ao armazenar token de acesso:', error);
    throw error;
  }
}

/**
 * Obtém token de acesso
 */
export async function getAccessToken(): Promise<string | null> {
  try {
    return await SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
  } catch (error) {
    console.error('Erro ao obter token de acesso:', error);
    return null;
  }
}

/**
 * Armazena token de refresh de forma segura
 */
export async function storeRefreshToken(token: string): Promise<void> {
  try {
    await SecureStore.setItemAsync(STORAGE_KEYS.REFRESH_TOKEN, token);
  } catch (error) {
    console.error('Erro ao armazenar token de refresh:', error);
    throw error;
  }
}

/**
 * Obtém token de refresh
 */
export async function getRefreshToken(): Promise<string | null> {
  try {
    return await SecureStore.getItemAsync(STORAGE_KEYS.REFRESH_TOKEN);
  } catch (error) {
    console.error('Erro ao obter token de refresh:', error);
    return null;
  }
}

/**
 * Armazena dados do usuário
 */
export async function storeUserData(user: AuthUser): Promise<void> {
  try {
    const userData = JSON.stringify(user);
    await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, userData);
  } catch (error) {
    console.error('Erro ao armazenar dados do usuário:', error);
    throw error;
  }
}

/**
 * Obtém dados do usuário
 */
export async function getUserData(): Promise<AuthUser | null> {
  try {
    const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Erro ao obter dados do usuário:', error);
    return null;
  }
}

/**
 * Armazena preferência de autenticação biométrica
 */
export async function storeBiometricEnabled(enabled: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.BIOMETRIC_ENABLED, JSON.stringify(enabled));
  } catch (error) {
    console.error('Erro ao armazenar preferência biométrica:', error);
    throw error;
  }
}

/**
 * Obtém preferência de autenticação biométrica
 */
export async function getBiometricEnabled(): Promise<boolean> {
  try {
    const enabled = await AsyncStorage.getItem(STORAGE_KEYS.BIOMETRIC_ENABLED);
    return enabled ? JSON.parse(enabled) : false;
  } catch (error) {
    console.error('Erro ao obter preferência biométrica:', error);
    return false;
  }
}

/**
 * Armazena preferência "Lembrar de mim"
 */
export async function storeRememberMe(remember: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.REMEMBER_ME, JSON.stringify(remember));
  } catch (error) {
    console.error('Erro ao armazenar preferência "Lembrar de mim":', error);
    throw error;
  }
}

/**
 * Obtém preferência "Lembrar de mim"
 */
export async function getRememberMe(): Promise<boolean> {
  try {
    const remember = await AsyncStorage.getItem(STORAGE_KEYS.REMEMBER_ME);
    return remember ? JSON.parse(remember) : false;
  } catch (error) {
    console.error('Erro ao obter preferência "Lembrar de mim":', error);
    return false;
  }
}

/**
 * Armazena último email usado
 */
export async function storeLastEmail(email: string): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.LAST_EMAIL, email);
  } catch (error) {
    console.error('Erro ao armazenar último email:', error);
    throw error;
  }
}

/**
 * Obtém último email usado
 */
export async function getLastEmail(): Promise<string | null> {
  try {
    return await AsyncStorage.getItem(STORAGE_KEYS.LAST_EMAIL);
  } catch (error) {
    console.error('Erro ao obter último email:', error);
    return null;
  }
}

/**
 * Armazena preferências do app
 */
export async function storeAppPreferences(preferences: Record<string, any>): Promise<void> {
  try {
    const preferencesData = JSON.stringify(preferences);
    await AsyncStorage.setItem(STORAGE_KEYS.APP_PREFERENCES, preferencesData);
  } catch (error) {
    console.error('Erro ao armazenar preferências do app:', error);
    throw error;
  }
}

/**
 * Obtém preferências do app
 */
export async function getAppPreferences(): Promise<Record<string, any> | null> {
  try {
    const preferences = await AsyncStorage.getItem(STORAGE_KEYS.APP_PREFERENCES);
    return preferences ? JSON.parse(preferences) : null;
  } catch (error) {
    console.error('Erro ao obter preferências do app:', error);
    return null;
  }
}

/**
 * Remove todos os dados de autenticação
 */
export async function clearAuthData(): Promise<void> {
  try {
    await Promise.all([
      SecureStore.deleteItemAsync(STORAGE_KEYS.ACCESS_TOKEN),
      SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN),
      AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
    ]);
  } catch (error) {
    console.error('Erro ao limpar dados de autenticação:', error);
    throw error;
  }
}

/**
 * Remove todos os dados do app
 */
export async function clearAllData(): Promise<void> {
  try {
    await Promise.all([
      clearAuthData(),
      AsyncStorage.removeItem(STORAGE_KEYS.BIOMETRIC_ENABLED),
      AsyncStorage.removeItem(STORAGE_KEYS.REMEMBER_ME),
      AsyncStorage.removeItem(STORAGE_KEYS.LAST_EMAIL),
      AsyncStorage.removeItem(STORAGE_KEYS.APP_PREFERENCES),
    ]);
  } catch (error) {
    console.error('Erro ao limpar todos os dados:', error);
    throw error;
  }
}
