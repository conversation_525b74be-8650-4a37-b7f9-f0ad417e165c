import { CPFValidation, RGValidation } from '../types/document';

/**
 * Valida CPF brasileiro
 */
export function validateCPF(cpf: string): CPFValidation {
  const cleanCPF = cpf.replace(/\D/g, '');
  const formatted = formatCPF(cleanCPF);
  
  const errors: string[] = [];
  
  // Verifica se tem 11 dígitos
  if (cleanCPF.length !== 11) {
    errors.push('CPF deve ter 11 dígitos');
  }
  
  // Verifica se não são todos os dígitos iguais
  if (/^(\d)\1{10}$/.test(cleanCPF)) {
    errors.push('CPF não pode ter todos os dígitos iguais');
  }
  
  // Validação do algoritmo do CPF
  if (cleanCPF.length === 11 && !isValidCPFAlgorithm(cleanCPF)) {
    errors.push('CPF inválido');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    cpf: cleanCPF,
    formatted
  };
}

/**
 * Valida RG brasileiro
 */
export function validateRG(rg: string, state?: string): RGValidation {
  const cleanRG = rg.replace(/\D/g, '');
  const formatted = formatRG(rg);
  
  const errors: string[] = [];
  
  // Verifica se tem pelo menos 7 dígitos
  if (cleanRG.length < 7 || cleanRG.length > 9) {
    errors.push('RG deve ter entre 7 e 9 dígitos');
  }
  
  // Verifica se não são todos os dígitos iguais
  if (/^(\d)\1+$/.test(cleanRG)) {
    errors.push('RG não pode ter todos os dígitos iguais');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    rg: cleanRG,
    formatted,
    state
  };
}

/**
 * Valida email
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Valida telefone brasileiro
 */
export function validatePhone(phone: string): boolean {
  const cleanPhone = phone.replace(/\D/g, '');
  // Aceita formatos: (11) 99999-9999 ou (11) 9999-9999
  return cleanPhone.length === 10 || cleanPhone.length === 11;
}

/**
 * Valida senha
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Senha deve ter pelo menos 8 caracteres');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra minúscula');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra maiúscula');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Senha deve conter pelo menos um número');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Senha deve conter pelo menos um caractere especial');
  }
  
  // Calcula força da senha
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  if (errors.length === 0) {
    strength = 'strong';
  } else if (errors.length <= 2) {
    strength = 'medium';
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    strength
  };
}

/**
 * Formata CPF
 */
export function formatCPF(cpf: string): string {
  const cleanCPF = cpf.replace(/\D/g, '');
  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Formata RG
 */
export function formatRG(rg: string): string {
  const cleanRG = rg.replace(/\D/g, '');
  if (cleanRG.length <= 8) {
    return cleanRG.replace(/(\d{2})(\d{3})(\d{3})/, '$1.$2.$3');
  }
  return cleanRG.replace(/(\d{2})(\d{3})(\d{3})(\d{1})/, '$1.$2.$3-$4');
}

/**
 * Formata telefone
 */
export function formatPhone(phone: string): string {
  const cleanPhone = phone.replace(/\D/g, '');
  if (cleanPhone.length === 10) {
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
}

/**
 * Algoritmo de validação do CPF
 */
function isValidCPFAlgorithm(cpf: string): boolean {
  let sum = 0;
  let remainder;
  
  // Validação do primeiro dígito verificador
  for (let i = 1; i <= 9; i++) {
    sum += parseInt(cpf.substring(i - 1, i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cpf.substring(9, 10))) return false;
  
  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 1; i <= 10; i++) {
    sum += parseInt(cpf.substring(i - 1, i)) * (12 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cpf.substring(10, 11))) return false;
  
  return true;
}

/**
 * Valida CEP
 */
export function validateCEP(cep: string): boolean {
  const cleanCEP = cep.replace(/\D/g, '');
  return cleanCEP.length === 8;
}

/**
 * Formata CEP
 */
export function formatCEP(cep: string): string {
  const cleanCEP = cep.replace(/\D/g, '');
  return cleanCEP.replace(/(\d{5})(\d{3})/, '$1-$2');
}
