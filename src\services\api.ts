import { getAccessToken, getRefreshToken, storeAccessToken } from './storage';

// Configuração da API
const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  requireAuth?: boolean;
}

/**
 * Cliente HTTP para comunicação com a API
 */
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  /**
   * Faz uma requisição HTTP
   */
  async request<T = any>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      requireAuth = true
    } = options;

    try {
      const url = `${this.baseURL}${endpoint}`;
      
      // Headers padrão
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers
      };

      // Adiciona token de autenticação se necessário
      if (requireAuth) {
        const token = await getAccessToken();
        if (token) {
          requestHeaders.Authorization = `Bearer ${token}`;
        }
      }

      // Configura o corpo da requisição
      const requestBody = body ? JSON.stringify(body) : undefined;

      // Faz a requisição
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: requestBody,
      });

      // Tenta renovar token se expirado
      if (response.status === 401 && requireAuth) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Tenta novamente com o novo token
          const newToken = await getAccessToken();
          if (newToken) {
            requestHeaders.Authorization = `Bearer ${newToken}`;
            const retryResponse = await fetch(url, {
              method,
              headers: requestHeaders,
              body: requestBody,
            });
            return await this.handleResponse<T>(retryResponse);
          }
        }
      }

      return await this.handleResponse<T>(response);
    } catch (error) {
      console.error('Erro na requisição API:', error);
      return {
        success: false,
        error: 'Erro de conexão com o servidor'
      };
    }
  }

  /**
   * Processa a resposta da API
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();

      if (response.ok) {
        return {
          success: true,
          data: data.data || data,
          message: data.message
        };
      } else {
        return {
          success: false,
          error: data.error || data.message || 'Erro desconhecido',
          data: data.data
        };
      }
    } catch (error) {
      return {
        success: false,
        error: 'Erro ao processar resposta do servidor'
      };
    }
  }

  /**
   * Renova o token de acesso
   */
  private async refreshAccessToken(): Promise<boolean> {
    try {
      const refreshToken = await getRefreshToken();
      if (!refreshToken) {
        return false;
      }

      const response = await this.request('/auth/refresh', {
        method: 'POST',
        body: { refreshToken },
        requireAuth: false
      });

      if (response.success && response.data?.accessToken) {
        await storeAccessToken(response.data.accessToken);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      return false;
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, requireAuth = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', requireAuth });
  }

  /**
   * POST request
   */
  async post<T = any>(
    endpoint: string,
    body?: any,
    requireAuth = true
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body, requireAuth });
  }

  /**
   * PUT request
   */
  async put<T = any>(
    endpoint: string,
    body?: any,
    requireAuth = true
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body, requireAuth });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, requireAuth = true): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', requireAuth });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(
    endpoint: string,
    body?: any,
    requireAuth = true
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body, requireAuth });
  }

  /**
   * Upload de arquivo
   */
  async uploadFile<T = any>(
    endpoint: string,
    file: {
      uri: string;
      name: string;
      type: string;
    },
    additionalData?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    try {
      const formData = new FormData();
      
      // Adiciona o arquivo
      formData.append('file', {
        uri: file.uri,
        name: file.name,
        type: file.type,
      } as any);

      // Adiciona dados adicionais
      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, additionalData[key]);
        });
      }

      const token = await getAccessToken();
      const headers: Record<string, string> = {
        'Content-Type': 'multipart/form-data',
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      console.error('Erro no upload de arquivo:', error);
      return {
        success: false,
        error: 'Erro ao fazer upload do arquivo'
      };
    }
  }
}

// Instância singleton do cliente API
export const apiClient = new ApiClient();

// Exports para facilitar o uso
export const api = {
  get: apiClient.get.bind(apiClient),
  post: apiClient.post.bind(apiClient),
  put: apiClient.put.bind(apiClient),
  delete: apiClient.delete.bind(apiClient),
  patch: apiClient.patch.bind(apiClient),
  upload: apiClient.uploadFile.bind(apiClient),
};
