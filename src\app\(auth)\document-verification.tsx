import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useState } from 'react';
import {
    Alert,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { DocumentUpload } from '../../components/forms/DocumentUpload';
import { Button } from '../../components/ui/Button';
import { colors } from '../../styles/colors';
import { DocumentType } from '../../types/document';

interface DocumentStatus {
  type: DocumentType;
  uploaded: boolean;
  documentId?: string;
}

const REQUIRED_DOCUMENTS: DocumentType[] = ['cpf', 'rg', 'comprovante_residencia'];

export default function DocumentVerificationScreen() {
  const [documents, setDocuments] = useState<DocumentStatus[]>(
    REQUIRED_DOCUMENTS.map(type => ({
      type,
      uploaded: false,
    }))
  );

  const handleUploadSuccess = (documentType: DocumentType, documentId: string) => {
    console.log('DocumentVerification: Upload bem-sucedido para', documentType, documentId);
    setDocuments(prev =>
      prev.map(doc =>
        doc.type === documentType
          ? { ...doc, uploaded: true, documentId }
          : doc
      )
    );
  };

  const handleUploadError = (error: string) => {
    console.error('DocumentVerification: Erro no upload:', error);
  };

  const handleSkipDocument = (documentType: DocumentType) => {
    console.log('DocumentVerification: Pulando documento', documentType);
    Alert.alert(
      'Documento Pulado',
      `O documento ${getDocumentName(documentType)} foi pulado. Você pode enviá-lo mais tarde nas configurações do perfil.`,
      [{ text: 'OK' }]
    );

    // Marca como "enviado" para permitir continuar, mas sem documentId
    setDocuments(prev =>
      prev.map(doc =>
        doc.type === documentType
          ? { ...doc, uploaded: true, documentId: 'skipped' }
          : doc
      )
    );
  };

  const handleSkipVerification = () => {
    Alert.alert(
      'Pular Verificação',
      'Você pode enviar seus documentos mais tarde nas configurações do perfil. Deseja continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Continuar',
          onPress: () => router.replace('/(tabs)'),
        },
      ]
    );
  };

  const handleContinue = () => {
    const allUploaded = documents.every(doc => doc.uploaded);

    if (allUploaded) {
      Alert.alert(
        'Documentos Enviados',
        'Seus documentos foram enviados com sucesso! Eles serão analisados em até 24 horas.',
        [
          {
            text: 'OK',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    } else {
      Alert.alert(
        'Documentos Pendentes',
        'Ainda há documentos obrigatórios que não foram enviados. Deseja continuar mesmo assim?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Continuar',
            onPress: () => router.replace('/(tabs)'),
          },
        ]
      );
    }
  };

  const getDocumentIcon = (doc: DocumentStatus) => {
    if (doc.uploaded) {
      return <Ionicons name="checkmark-circle" size={24} color={colors.green[300]} />;
    }
    return <Ionicons name="document" size={24} color={colors.gray[400]} />;
  };

  const getDocumentName = (type: DocumentType) => {
    switch (type) {
      case 'cpf':
        return 'CPF';
      case 'rg':
        return 'RG';
      case 'comprovante_residencia':
        return 'Comprovante de Residência';
      default:
        return type;
    }
  };

  const allDocumentsUploaded = documents.every(doc => doc.uploaded);
  const uploadedCount = documents.filter(doc => doc.uploaded).length;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkipVerification}
        >
          <Text style={styles.skipText}>Pular</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Verificação de Documentos</Text>
          <Text style={styles.subtitle}>
            Para sua segurança e dos outros usuários, precisamos verificar alguns documentos
          </Text>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>Progresso</Text>
            <Text style={styles.progressText}>
              {uploadedCount} de {documents.length} documentos
            </Text>
          </View>

          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                { width: `${(uploadedCount / documents.length) * 100}%` }
              ]}
            />
          </View>

          <View style={styles.documentsList}>
            {documents.map((doc) => (
              <View key={doc.type} style={styles.documentItem}>
                {getDocumentIcon(doc)}
                <Text style={[
                  styles.documentName,
                  doc.uploaded && styles.documentNameCompleted
                ]}>
                  {getDocumentName(doc.type)}
                </Text>
                {doc.uploaded && (
                  <Text style={styles.completedText}>Enviado</Text>
                )}
              </View>
            ))}
          </View>
        </View>

        <View style={styles.documentsContainer}>
          {REQUIRED_DOCUMENTS.map((documentType) => {
            const doc = documents.find(d => d.type === documentType);
            if (doc?.uploaded) {
              return (
                <View key={documentType} style={styles.completedDocument}>
                  <View style={styles.completedHeader}>
                    <Ionicons name="checkmark-circle" size={24} color={colors.green[300]} />
                    <Text style={styles.completedTitle}>
                      {getDocumentName(documentType)}
                    </Text>
                  </View>
                  <Text style={styles.completedMessage}>
                    Documento enviado com sucesso!
                  </Text>
                </View>
              );
            }

            return (
              <DocumentUpload
                key={documentType}
                documentType={documentType}
                onUploadSuccess={(documentId) => handleUploadSuccess(documentType, documentId)}
                onUploadError={handleUploadError}
                onSkip={() => handleSkipDocument(documentType)}
              />
            );
          })}
        </View>

        <View style={styles.infoContainer}>
          <View style={styles.infoItem}>
            <Ionicons name="shield-checkmark" size={20} color={colors.sky[600]} />
            <Text style={styles.infoText}>
              Seus documentos são criptografados e armazenados com segurança
            </Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="time" size={20} color={colors.sky[600]} />
            <Text style={styles.infoText}>
              A verificação leva até 24 horas úteis
            </Text>
          </View>

          <View style={styles.infoItem}>
            <Ionicons name="eye-off" size={20} color={colors.sky[600]} />
            <Text style={styles.infoText}>
              Apenas nossa equipe de verificação tem acesso aos documentos
            </Text>
          </View>
        </View>

        <View style={styles.actions}>
          <Button
            title={allDocumentsUploaded ? 'Finalizar Verificação' : 'Continuar'}
            onPress={handleContinue}
            style={styles.continueButton}
          />

          <TouchableOpacity onPress={handleSkipVerification}>
            <Text style={styles.skipLaterText}>
              Enviar documentos mais tarde
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 8,
  },

  skipButton: {
    padding: 8,
  },

  skipText: {
    fontSize: 16,
    color: colors.sky[600],
    fontWeight: '600',
  },

  content: {
    flex: 1,
    paddingHorizontal: 24,
  },

  titleContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },

  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 8,
    textAlign: 'center',
  },

  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 24,
  },

  progressContainer: {
    backgroundColor: colors.gray[100],
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },

  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },

  progressText: {
    fontSize: 14,
    color: colors.gray[600],
  },

  progressBar: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    marginBottom: 16,
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.sky[600],
    borderRadius: 4,
  },

  documentsList: {
    gap: 8,
  },

  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  documentName: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[600],
  },

  documentNameCompleted: {
    color: colors.gray[900],
    fontWeight: '500',
  },

  completedText: {
    fontSize: 12,
    color: colors.green[300],
    fontWeight: '600',
  },

  documentsContainer: {
    marginBottom: 32,
  },

  completedDocument: {
    backgroundColor: colors.green[50],
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.green[200],
  },

  completedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  completedTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginLeft: 8,
  },

  completedMessage: {
    fontSize: 14,
    color: colors.gray[600],
  },

  infoContainer: {
    backgroundColor: colors.sky[50],
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
    gap: 12,
  },

  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },

  infoText: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },

  actions: {
    paddingBottom: 32,
    alignItems: 'center',
  },

  continueButton: {
    marginBottom: 16,
    minWidth: 200,
  },

  skipLaterText: {
    fontSize: 14,
    color: colors.gray[500],
    textAlign: 'center',
  },
});
