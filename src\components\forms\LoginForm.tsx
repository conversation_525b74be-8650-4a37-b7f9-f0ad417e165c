import { Ionicons } from '@expo/vector-icons';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import * as yup from 'yup';

import { useAuth } from '../../contexts/AuthContext';
import { getBiometricEnabled, getLastEmail } from '../../services/storage';
import { colors } from '../../styles/colors';
import { LoginCredentials } from '../../types/auth';
import {
  authenticateWithBiometric,
  getBiometryDisplayName,
  isBiometricSupported
} from '../../utils/biometric';
import { validateEmail } from '../../utils/validation';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

// Schema de validação
const loginSchema = yup.object().shape({
  email: yup
    .string()
    .required('Email é obrigatório')
    .test('email', 'Email inválido', (value) => {
      return value ? validateEmail(value) : false;
    }),
  password: yup
    .string()
    .required('Senha é obrigatória')
    .min(6, 'Senha deve ter pelo menos 6 caracteres'),
  rememberMe: yup.boolean().optional(),
});

interface LoginFormProps {
  onForgotPassword: () => void;
  onRegister: () => void;
}

export function LoginForm({ onForgotPassword, onRegister }: LoginFormProps) {
  const { login, isLoading, error } = useAuth();
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricType, setBiometricType] = useState<string>('');

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<LoginCredentials>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  // Verifica disponibilidade de biometria ao montar o componente
  useEffect(() => {
    checkBiometricAvailability();
    loadLastEmail();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const isSupported = await isBiometricSupported();
      const isEnabled = await getBiometricEnabled();

      setBiometricAvailable(isSupported);
      setBiometricEnabled(isEnabled);

      if (isSupported) {
        // Aqui você poderia obter o tipo específico de biometria
        setBiometricType('biometria');
      }
    } catch (error) {
      console.error('Erro ao verificar biometria:', error);
    }
  };

  const loadLastEmail = async () => {
    try {
      const lastEmail = await getLastEmail();
      if (lastEmail) {
        setValue('email', lastEmail);
      }
    } catch (error) {
      console.error('Erro ao carregar último email:', error);
    }
  };

  const onSubmit = async (data: LoginCredentials) => {
    try {
      await login(data);
    } catch (error) {
      console.error('Erro no login:', error);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      const result = await authenticateWithBiometric(
        'Autentique-se para fazer login'
      );

      if (result.success) {
        // Aqui você implementaria a lógica para login com biometria
        // Por exemplo, usar credenciais salvas de forma segura
        Alert.alert(
          'Sucesso',
          'Autenticação biométrica realizada com sucesso!'
        );
      } else {
        Alert.alert(
          'Erro',
          result.error || 'Falha na autenticação biométrica'
        );
      }
    } catch (error) {
      console.error('Erro na autenticação biométrica:', error);
      Alert.alert('Erro', 'Erro interno na autenticação biométrica');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Entrar</Text>
        <Text style={styles.subtitle}>
          Acesse sua conta para continuar
        </Text>
      </View>

      <View style={styles.form}>
        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Email"
              placeholder="<EMAIL>"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.email?.message}
              leftIcon="mail"
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Senha"
              placeholder="Sua senha"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.password?.message}
              leftIcon="lock-closed"
              secureTextEntry
              autoComplete="password"
            />
          )}
        />

        <View style={styles.options}>
          <Controller
            control={control}
            name="rememberMe"
            render={({ field: { onChange, value } }) => (
              <TouchableOpacity
                style={styles.rememberMe}
                onPress={() => onChange(!value)}
              >
                <Ionicons
                  name={value ? 'checkbox' : 'checkbox-outline'}
                  size={20}
                  color={value ? colors.sky[600] : colors.gray[400]}
                />
                <Text style={styles.rememberMeText}>Lembrar de mim</Text>
              </TouchableOpacity>
            )}
          />

          <TouchableOpacity onPress={onForgotPassword}>
            <Text style={styles.forgotPassword}>Esqueci minha senha</Text>
          </TouchableOpacity>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <Button
          title="Entrar"
          onPress={handleSubmit(onSubmit)}
          loading={isLoading}
          style={styles.loginButton}
        />

        {biometricAvailable && biometricEnabled && (
          <Button
            title={`Entrar com ${getBiometryDisplayName(biometricType)}`}
            onPress={handleBiometricLogin}
            variant="outline"
            style={styles.biometricButton}
            icon={
              <Ionicons
                name="finger-print"
                size={20}
                color={colors.sky[600]}
                style={{ marginRight: 8 }}
              />
            }
          />
        )}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>Não tem uma conta? </Text>
        <TouchableOpacity onPress={onRegister}>
          <Text style={styles.registerLink}>Cadastre-se</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },

  header: {
    marginBottom: 32,
    alignItems: 'center',
  },

  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },

  form: {
    marginBottom: 32,
  },

  options: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },

  rememberMe: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  rememberMeText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.gray[600],
  },

  forgotPassword: {
    fontSize: 14,
    color: colors.sky[600],
    fontWeight: '600',
  },

  errorContainer: {
    backgroundColor: colors.red[50],
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },

  errorText: {
    color: colors.red[300],
    fontSize: 14,
    textAlign: 'center',
  },

  loginButton: {
    marginBottom: 16,
  },

  biometricButton: {
    marginBottom: 16,
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },

  footerText: {
    fontSize: 14,
    color: colors.gray[600],
  },

  registerLink: {
    fontSize: 14,
    color: colors.sky[600],
    fontWeight: '600',
  },
});
