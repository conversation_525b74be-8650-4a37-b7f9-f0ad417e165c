import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { Button } from '../../components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import {
    getBiometricEnabled,
    storeBiometricEnabled
} from '../../services/storage';
import { colors } from '../../styles/colors';
import {
    getBiometryDisplayName,
    isBiometricSupported
} from '../../utils/biometric';

export default function ProfileScreen() {
  const { user, logout } = useAuth();
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricSupported, setBiometricSupported] = useState(false);

  React.useEffect(() => {
    checkBiometricSettings();
  }, []);

  const checkBiometricSettings = async () => {
    try {
      const [isSupported, isEnabled] = await Promise.all([
        isBiometricSupported(),
        getBiometricEnabled(),
      ]);

      setBiometricSupported(isSupported);
      setBiometricEnabled(isEnabled);
    } catch (error) {
      console.error('Erro ao verificar configurações biométricas:', error);
    }
  };

  const handleBiometricToggle = async () => {
    try {
      const newValue = !biometricEnabled;
      await storeBiometricEnabled(newValue);
      setBiometricEnabled(newValue);

      Alert.alert(
        'Configuração Atualizada',
        `Autenticação biométrica ${newValue ? 'ativada' : 'desativada'} com sucesso!`
      );
    } catch (error) {
      console.error('Erro ao alterar configuração biométrica:', error);
      Alert.alert('Erro', 'Não foi possível alterar a configuração');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Sair',
      'Tem certeza que deseja sair da sua conta?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Sair',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/(auth)/login');
            } catch (error) {
              console.error('Erro no logout:', error);
            }
          },
        },
      ]
    );
  };

  const handleEditProfile = () => {
    // Implementar navegação para edição de perfil
    Alert.alert('Em Desenvolvimento', 'Funcionalidade em desenvolvimento');
  };

  const handleDocumentVerification = () => {
    router.push('/(auth)/document-verification');
  };

  const getUserTypeDisplay = () => {
    switch (user?.userType) {
      case 'locador':
        return 'Locador';
      case 'locatario':
        return 'Locatário';
      case 'ambos':
        return 'Locador e Locatário';
      default:
        return 'Não definido';
    }
  };

  const getVerificationStatusColor = (isVerified: boolean) => {
    return isVerified ? colors.green[300] : colors.yellow[300];
  };

  const getVerificationStatusText = (isVerified: boolean) => {
    return isVerified ? 'Verificado' : 'Pendente';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Perfil</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Informações do usuário */}
        <View style={styles.userCard}>
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              {user?.profilePicture ? (
                <Image source={{ uri: user.profilePicture }} style={styles.avatarImage} />
              ) : (
                <Ionicons name="person" size={40} color={colors.gray[400]} />
              )}
            </View>

            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user?.fullName}</Text>
              <Text style={styles.userEmail}>{user?.email}</Text>
              <Text style={styles.userType}>{getUserTypeDisplay()}</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
            <Ionicons name="pencil" size={20} color={colors.sky[600]} />
          </TouchableOpacity>
        </View>

        {/* Status de verificação */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status de Verificação</Text>

          <View style={styles.statusItem}>
            <View style={styles.statusInfo}>
              <Ionicons name="mail" size={20} color={colors.gray[600]} />
              <Text style={styles.statusLabel}>Email</Text>
            </View>
            <Text style={[
              styles.statusValue,
              { color: getVerificationStatusColor(user?.isEmailVerified || false) }
            ]}>
              {getVerificationStatusText(user?.isEmailVerified || false)}
            </Text>
          </View>

          <View style={styles.statusItem}>
            <View style={styles.statusInfo}>
              <Ionicons name="document-text" size={20} color={colors.gray[600]} />
              <Text style={styles.statusLabel}>Documentos</Text>
            </View>
            <View style={styles.statusRight}>
              <Text style={[
                styles.statusValue,
                { color: getVerificationStatusColor(user?.isDocumentVerified || false) }
              ]}>
                {getVerificationStatusText(user?.isDocumentVerified || false)}
              </Text>
              {!user?.isDocumentVerified && (
                <TouchableOpacity onPress={handleDocumentVerification}>
                  <Text style={styles.actionLink}>Verificar</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          <View style={styles.statusItem}>
            <View style={styles.statusInfo}>
              <Ionicons name="call" size={20} color={colors.gray[600]} />
              <Text style={styles.statusLabel}>Telefone</Text>
            </View>
            <Text style={[
              styles.statusValue,
              { color: getVerificationStatusColor(user?.isPhoneVerified || false) }
            ]}>
              {getVerificationStatusText(user?.isPhoneVerified || false)}
            </Text>
          </View>
        </View>

        {/* Configurações */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Configurações</Text>

          {biometricSupported && (
            <TouchableOpacity style={styles.settingItem} onPress={handleBiometricToggle}>
              <View style={styles.settingInfo}>
                <Ionicons name="finger-print" size={20} color={colors.gray[600]} />
                <Text style={styles.settingLabel}>
                  {getBiometryDisplayName('biometria')}
                </Text>
              </View>
              <View style={[
                styles.toggle,
                biometricEnabled && styles.toggleActive
              ]}>
                <View style={[
                  styles.toggleThumb,
                  biometricEnabled && styles.toggleThumbActive
                ]} />
              </View>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="notifications" size={20} color={colors.gray[600]} />
              <Text style={styles.settingLabel}>Notificações</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray[400]} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="shield-checkmark" size={20} color={colors.gray[600]} />
              <Text style={styles.settingLabel}>Privacidade</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray[400]} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Ionicons name="help-circle" size={20} color={colors.gray[600]} />
              <Text style={styles.settingLabel}>Ajuda</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray[400]} />
          </TouchableOpacity>
        </View>

        {/* Ações */}
        <View style={styles.actions}>
          <Button
            title="Sair da Conta"
            onPress={handleLogout}
            variant="outline"
            style={styles.logoutButton}
            textStyle={{ color: colors.red[300] }}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  header: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 8,
  },

  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.gray[900],
  },

  content: {
    flex: 1,
    paddingHorizontal: 24,
  },

  userCard: {
    backgroundColor: colors.gray[100],
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },

  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },

  userDetails: {
    flex: 1,
  },

  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 4,
  },

  userEmail: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },

  userType: {
    fontSize: 12,
    color: colors.sky[600],
    fontWeight: '600',
  },

  editButton: {
    padding: 8,
  },

  section: {
    marginBottom: 24,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 16,
  },

  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },

  statusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  statusLabel: {
    fontSize: 16,
    color: colors.gray[700],
    marginLeft: 12,
  },

  statusRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },

  statusValue: {
    fontSize: 14,
    fontWeight: '600',
  },

  actionLink: {
    fontSize: 14,
    color: colors.sky[600],
    fontWeight: '600',
  },

  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },

  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  settingLabel: {
    fontSize: 16,
    color: colors.gray[700],
    marginLeft: 12,
  },

  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.gray[300],
    justifyContent: 'center',
    paddingHorizontal: 2,
  },

  toggleActive: {
    backgroundColor: colors.sky[600],
  },

  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: colors.gray[50],
    alignSelf: 'flex-start',
  },

  toggleThumbActive: {
    alignSelf: 'flex-end',
  },

  actions: {
    paddingBottom: 32,
  },

  logoutButton: {
    borderColor: colors.red[300],
  },
});
