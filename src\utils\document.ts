import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';
import { DocumentRequirement, FileUpload } from '../types/document';

/**
 * Solicita permissão para acessar a câmera
 */
export async function requestCameraPermission(): Promise<boolean> {
  try {
    console.log('Solicitando permissão da câmera...');
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    console.log('Status da permissão da câmera:', status);

    if (status !== 'granted') {
      Alert.alert(
        'Permissão Necessária',
        'Para capturar documentos, precisamos de acesso à câmera. Por favor, permita o acesso nas configurações do seu dispositivo.',
        [{ text: 'OK' }]
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao solicitar permissão da câmera:', error);
    Alert.alert('Erro', 'Não foi possível solicitar permissão da câmera');
    return false;
  }
}

/**
 * Solicita permissão para acessar a galeria
 */
export async function requestMediaLibraryPermission(): Promise<boolean> {
  try {
    console.log('Solicitando permissão da galeria...');
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    console.log('Status da permissão da galeria:', status);

    if (status !== 'granted') {
      Alert.alert(
        'Permissão Necessária',
        'Para selecionar documentos da galeria, precisamos de acesso às suas fotos. Por favor, permita o acesso nas configurações do seu dispositivo.',
        [{ text: 'OK' }]
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao solicitar permissão da galeria:', error);
    Alert.alert('Erro', 'Não foi possível solicitar permissão da galeria');
    return false;
  }
}

/**
 * Abre a câmera para capturar documento
 */
export async function captureDocumentWithCamera(): Promise<FileUpload | null> {
  try {
    console.log('Iniciando captura com câmera...');

    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      console.log('Permissão da câmera negada');
      return null;
    }

    console.log('Abrindo câmera...');
    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      base64: false,
    });

    console.log('Resultado da câmera:', result);

    if (!result.canceled && result.assets && result.assets[0]) {
      const asset = result.assets[0];
      const file = {
        uri: asset.uri,
        name: asset.fileName || `document_${Date.now()}.jpg`,
        type: 'image/jpeg',
        size: asset.fileSize || 0
      };

      console.log('Arquivo capturado:', file);
      return file;
    }

    console.log('Captura cancelada ou sem assets');
    return null;
  } catch (error) {
    console.error('Erro ao capturar documento com câmera:', error);
    Alert.alert('Erro', 'Não foi possível acessar a câmera. Verifique as permissões do aplicativo.');
    return null;
  }
}

/**
 * Abre a galeria para selecionar documento
 */
export async function selectDocumentFromGallery(): Promise<FileUpload | null> {
  try {
    console.log('Iniciando seleção da galeria...');

    const hasPermission = await requestMediaLibraryPermission();
    if (!hasPermission) {
      console.log('Permissão da galeria negada');
      return null;
    }

    console.log('Abrindo galeria...');
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      base64: false,
    });

    console.log('Resultado da galeria:', result);

    if (!result.canceled && result.assets && result.assets[0]) {
      const asset = result.assets[0];
      const file = {
        uri: asset.uri,
        name: asset.fileName || `document_${Date.now()}.jpg`,
        type: 'image/jpeg',
        size: asset.fileSize || 0
      };

      console.log('Arquivo selecionado da galeria:', file);
      return file;
    }

    console.log('Seleção cancelada ou sem assets');
    return null;
  } catch (error) {
    console.error('Erro ao selecionar documento da galeria:', error);
    Alert.alert('Erro', 'Não foi possível acessar a galeria. Verifique as permissões do aplicativo.');
    return null;
  }
}

/**
 * Abre o seletor de documentos (PDF, imagens, etc.)
 */
export async function selectDocument(): Promise<FileUpload | null> {
  try {
    console.log('Iniciando seleção de documento...');

    const result = await DocumentPicker.getDocumentAsync({
      type: ['image/*', 'application/pdf'],
      copyToCacheDirectory: true,
    });

    console.log('Resultado do seletor de documentos:', result);

    if (!result.canceled && result.assets && result.assets[0]) {
      const asset = result.assets[0];
      const file = {
        uri: asset.uri,
        name: asset.name,
        type: asset.mimeType || 'application/octet-stream',
        size: asset.size || 0
      };

      console.log('Documento selecionado:', file);
      return file;
    }

    console.log('Seleção de documento cancelada ou sem assets');
    return null;
  } catch (error) {
    console.error('Erro ao selecionar documento:', error);
    Alert.alert('Erro', 'Não foi possível selecionar o arquivo. Tente novamente.');
    return null;
  }
}

/**
 * Valida se o arquivo atende aos requisitos
 */
export function validateDocumentFile(
  file: FileUpload,
  requirement: DocumentRequirement
): { isValid: boolean; errors: string[] } {
  console.log('Validando arquivo:', file);
  console.log('Requisitos:', requirement);

  const errors: string[] = [];

  // Verifica formato - mais permissivo para imagens
  const isImageFormat = file.type.startsWith('image/') ||
                       requirement.formats.some(format => format.includes('image'));
  const isPdfFormat = file.type === 'application/pdf' ||
                     requirement.formats.includes('application/pdf');

  if (!isImageFormat && !isPdfFormat && !requirement.formats.includes(file.type)) {
    errors.push(`Formato não suportado. Formatos aceitos: ${requirement.formats.join(', ')}`);
  }

  // Verifica tamanho (converte de bytes para MB) - mais permissivo
  const maxSizeBytes = requirement.maxSize * 1024 * 1024;
  if (file.size && file.size > maxSizeBytes) {
    const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);
    errors.push(`Arquivo muito grande (${fileSizeMB}MB). Tamanho máximo: ${requirement.maxSize}MB`);
  }

  console.log('Resultado da validação:', { isValid: errors.length === 0, errors });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Comprime imagem se necessário
 */
export async function compressImageIfNeeded(
  uri: string,
  maxSizeMB: number = 2
): Promise<string> {
  try {
    // Esta função seria implementada com uma biblioteca de compressão
    // Por enquanto, retorna a URI original
    return uri;
  } catch (error) {
    console.error('Erro ao comprimir imagem:', error);
    return uri;
  }
}

/**
 * Gera nome único para arquivo
 */
export function generateUniqueFileName(originalName: string, documentType: string): string {
  const timestamp = Date.now();
  const extension = originalName.split('.').pop() || 'jpg';
  return `${documentType}_${timestamp}.${extension}`;
}

/**
 * Converte arquivo para base64 (se necessário para upload)
 */
export async function fileToBase64(uri: string): Promise<string> {
  try {
    // Esta função seria implementada para converter arquivo para base64
    // Por enquanto, retorna a URI
    return uri;
  } catch (error) {
    console.error('Erro ao converter arquivo para base64:', error);
    throw error;
  }
}

/**
 * Obtém informações do arquivo
 */
export async function getFileInfo(uri: string): Promise<{
  size: number;
  type: string;
  name: string;
}> {
  try {
    // Esta função seria implementada para obter informações detalhadas do arquivo
    // Por enquanto, retorna informações básicas
    return {
      size: 0,
      type: 'image/jpeg',
      name: 'document.jpg'
    };
  } catch (error) {
    console.error('Erro ao obter informações do arquivo:', error);
    throw error;
  }
}
