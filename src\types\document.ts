export interface DocumentValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface CPFValidation extends DocumentValidation {
  cpf: string;
  formatted: string;
}

export interface RGValidation extends DocumentValidation {
  rg: string;
  formatted: string;
  state?: string;
}

export interface DocumentUploadResult {
  success: boolean;
  documentId?: string;
  error?: string;
  uploadedAt?: string;
}

export interface DocumentVerificationResult {
  success: boolean;
  status: 'approved' | 'rejected' | 'pending';
  confidence?: number;
  extractedData?: {
    name?: string;
    document?: string;
    dateOfBirth?: string;
    address?: string;
  };
  rejectionReasons?: string[];
}

export interface FileUpload {
  uri: string;
  name: string;
  type: string;
  size?: number;
}

export interface DocumentRequirement {
  type: string;
  name: string;
  description: string;
  required: boolean;
  formats: string[];
  maxSize: number; // in MB
  examples?: string[];
}

export const DOCUMENT_REQUIREMENTS: Record<string, DocumentRequirement> = {
  cpf: {
    type: 'cpf',
    name: 'CPF',
    description: 'Documento de identificação fiscal',
    required: true,
    formats: ['image/jpeg', 'image/png', 'application/pdf'],
    maxSize: 5,
    examples: ['Frente e verso do CPF', 'Comprovante de situação cadastral']
  },
  rg: {
    type: 'rg',
    name: 'RG',
    description: 'Registro Geral (Carteira de Identidade)',
    required: true,
    formats: ['image/jpeg', 'image/png', 'application/pdf'],
    maxSize: 5,
    examples: ['Frente e verso do RG']
  },
  comprovante_residencia: {
    type: 'comprovante_residencia',
    name: 'Comprovante de Residência',
    description: 'Documento que comprove seu endereço atual',
    required: true,
    formats: ['image/jpeg', 'image/png', 'application/pdf'],
    maxSize: 5,
    examples: [
      'Conta de luz, água ou gás',
      'Extrato bancário',
      'Contrato de aluguel',
      'IPTU'
    ]
  },
  comprovante_renda: {
    type: 'comprovante_renda',
    name: 'Comprovante de Renda',
    description: 'Documento que comprove sua renda mensal',
    required: false,
    formats: ['image/jpeg', 'image/png', 'application/pdf'],
    maxSize: 5,
    examples: [
      'Holerite',
      'Declaração de Imposto de Renda',
      'Extrato bancário',
      'Contrato de trabalho'
    ]
  }
};
