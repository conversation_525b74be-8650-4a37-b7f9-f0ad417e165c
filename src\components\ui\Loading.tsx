import React from 'react';
import {
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  Modal,
  ViewStyle,
} from 'react-native';
import { colors } from '../../styles/colors';

interface LoadingProps {
  visible?: boolean;
  message?: string;
  overlay?: boolean;
  size?: 'small' | 'large';
  color?: string;
  style?: ViewStyle;
}

export function Loading({
  visible = true,
  message,
  overlay = false,
  size = 'large',
  color = colors.sky[600],
  style,
}: LoadingProps) {
  if (overlay) {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        statusBarTranslucent
      >
        <View style={styles.overlay}>
          <View style={styles.overlayContent}>
            <ActivityIndicator size={size} color={color} />
            {message && <Text style={styles.overlayMessage}>{message}</Text>}
          </View>
        </View>
      </Modal>
    );
  }

  if (!visible) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <ActivityIndicator size={size} color={color} />
      {message && <Text style={styles.message}>{message}</Text>}
    </View>
  );
}

// Componente para loading inline
export function InlineLoading({
  message,
  size = 'small',
  color = colors.sky[600],
  style,
}: Omit<LoadingProps, 'visible' | 'overlay'>) {
  return (
    <View style={[styles.inlineContainer, style]}>
      <ActivityIndicator size={size} color={color} />
      {message && <Text style={styles.inlineMessage}>{message}</Text>}
    </View>
  );
}

// Componente para loading de página inteira
export function PageLoading({
  message = 'Carregando...',
  color = colors.sky[600],
}: Pick<LoadingProps, 'message' | 'color'>) {
  return (
    <View style={styles.pageContainer}>
      <ActivityIndicator size="large" color={color} />
      <Text style={styles.pageMessage}>{message}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  
  message: {
    marginTop: 12,
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  overlayContent: {
    backgroundColor: colors.gray[50],
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 120,
  },
  
  overlayMessage: {
    marginTop: 12,
    fontSize: 14,
    color: colors.gray[700],
    textAlign: 'center',
  },
  
  inlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  
  inlineMessage: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.gray[600],
  },
  
  pageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.gray[50],
    padding: 20,
  },
  
  pageMessage: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
});
