export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  phone: string;
  userType: UserType;
  acceptTerms: boolean;
}

export interface AuthUser {
  id: string;
  email: string;
  fullName: string;
  phone: string;
  userType: UserType;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  profilePicture?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  verifyPhone: (code: string) => Promise<void>;
  updateProfile: (data: Partial<AuthUser>) => Promise<void>;
  refreshToken: () => Promise<void>;
  clearError: () => void;
}

export type UserType = 'locador' | 'locatario' | 'ambos';

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  biometryType?: 'fingerprint' | 'faceId' | 'iris' | 'none';
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface VerificationCodeData {
  code: string;
  type: 'email' | 'phone';
}
