import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';

import { colors } from '../../styles/colors';
import { useAuth } from '../../contexts/AuthContext';

export default function HomeScreen() {
  const { user } = useAuth();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.greeting}>
            Olá, {user?.fullName?.split(' ')[0] || 'Usuário'}!
          </Text>
          <Text style={styles.subtitle}>
            Bem-vindo ao seu app de locação
          </Text>
        </View>

        <View style={styles.welcomeCard}>
          <Text style={styles.welcomeTitle}>
            🎉 Conta criada com sucesso!
          </Text>
          <Text style={styles.welcomeText}>
            Sua conta foi criada e você já pode começar a usar o app. 
            {!user?.isEmailVerified && ' Não esqueça de verificar seu email.'}
            {!user?.isDocumentVerified && ' Complete a verificação de documentos para ter acesso total.'}
          </Text>
        </View>

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Ações Rápidas</Text>
          
          <View style={styles.actionCard}>
            <Text style={styles.actionTitle}>🏠 Buscar Imóveis</Text>
            <Text style={styles.actionDescription}>
              Encontre o imóvel perfeito para você
            </Text>
          </View>

          <View style={styles.actionCard}>
            <Text style={styles.actionTitle}>📋 Anunciar Imóvel</Text>
            <Text style={styles.actionDescription}>
              Coloque seu imóvel para alugar
            </Text>
          </View>

          <View style={styles.actionCard}>
            <Text style={styles.actionTitle}>⭐ Favoritos</Text>
            <Text style={styles.actionDescription}>
              Veja seus imóveis salvos
            </Text>
          </View>
        </View>

        <View style={styles.statusCard}>
          <Text style={styles.statusTitle}>Status da Conta</Text>
          
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Email:</Text>
            <Text style={[
              styles.statusValue,
              { color: user?.isEmailVerified ? colors.green[300] : colors.yellow[300] }
            ]}>
              {user?.isEmailVerified ? 'Verificado' : 'Pendente'}
            </Text>
          </View>

          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Documentos:</Text>
            <Text style={[
              styles.statusValue,
              { color: user?.isDocumentVerified ? colors.green[300] : colors.yellow[300] }
            ]}>
              {user?.isDocumentVerified ? 'Verificados' : 'Pendente'}
            </Text>
          </View>

          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Tipo de usuário:</Text>
            <Text style={styles.statusValue}>
              {user?.userType === 'locador' ? 'Locador' : 
               user?.userType === 'locatario' ? 'Locatário' : 'Ambos'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },

  content: {
    flex: 1,
    paddingHorizontal: 24,
  },

  header: {
    paddingTop: 24,
    paddingBottom: 32,
  },

  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },

  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
  },

  welcomeCard: {
    backgroundColor: colors.sky[50],
    padding: 20,
    borderRadius: 12,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: colors.sky[200],
  },

  welcomeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 8,
  },

  welcomeText: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },

  quickActions: {
    marginBottom: 32,
  },

  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 16,
  },

  actionCard: {
    backgroundColor: colors.gray[100],
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },

  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 4,
  },

  actionDescription: {
    fontSize: 14,
    color: colors.gray[600],
  },

  statusCard: {
    backgroundColor: colors.gray[100],
    padding: 20,
    borderRadius: 12,
    marginBottom: 32,
  },

  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 16,
  },

  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  statusLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },

  statusValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[900],
  },
});
