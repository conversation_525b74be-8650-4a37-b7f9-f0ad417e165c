import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { 
  AuthContextType, 
  AuthState, 
  AuthUser, 
  LoginCredentials, 
  RegisterData,
  ForgotPasswordData,
  ResetPasswordData,
  VerificationCodeData
} from '../types/auth';
import { UpdateProfileData } from '../types/user';
import { authService } from '../services/auth';
import { getUserData, getAccessToken } from '../services/storage';

// Estado inicial
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Tipos de ação
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: AuthUser | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' }
  | { type: 'LOGOUT' };

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    
    default:
      return state;
  }
}

// Contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider
interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Verifica se o usuário está autenticado ao inicializar
  useEffect(() => {
    checkAuthStatus();
  }, []);

  /**
   * Verifica status de autenticação
   */
  const checkAuthStatus = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });

      const [userData, token] = await Promise.all([
        getUserData(),
        getAccessToken(),
      ]);

      if (userData && token) {
        // Verifica se o token ainda é válido fazendo uma requisição
        const result = await authService.getCurrentUser();
        
        if (result.success && result.user) {
          dispatch({ type: 'SET_USER', payload: result.user });
        } else {
          // Token inválido, faz logout
          await authService.logout();
          dispatch({ type: 'LOGOUT' });
        }
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    } catch (error) {
      console.error('Erro ao verificar status de autenticação:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * Faz login
   */
  const login = async (credentials: LoginCredentials) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.login(credentials);

      if (result.success && result.user) {
        dispatch({ type: 'SET_USER', payload: result.user });
      } else {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao fazer login' });
      }
    } catch (error) {
      console.error('Erro no login:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Faz registro
   */
  const register = async (data: RegisterData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.register(data);

      if (result.success && result.user) {
        dispatch({ type: 'SET_USER', payload: result.user });
      } else {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao criar conta' });
      }
    } catch (error) {
      console.error('Erro no registro:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Faz logout
   */
  const logout = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await authService.logout();
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Erro no logout:', error);
      // Mesmo com erro, faz logout local
      dispatch({ type: 'LOGOUT' });
    }
  };

  /**
   * Solicita recuperação de senha
   */
  const forgotPassword = async (email: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.forgotPassword({ email });

      if (!result.success) {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao solicitar recuperação' });
      }

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Erro na recuperação de senha:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Redefine senha
   */
  const resetPassword = async (token: string, newPassword: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.resetPassword({ 
        token, 
        newPassword,
        confirmPassword: newPassword 
      });

      if (!result.success) {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao redefinir senha' });
      }

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Verifica email
   */
  const verifyEmail = async (token: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.verifyEmail({ code: token, type: 'email' });

      if (result.success) {
        // Atualiza dados do usuário
        await refreshToken();
      } else {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao verificar email' });
      }

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Erro na verificação de email:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Verifica telefone
   */
  const verifyPhone = async (code: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.verifyPhone({ code, type: 'phone' });

      if (result.success) {
        // Atualiza dados do usuário
        await refreshToken();
      } else {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao verificar telefone' });
      }

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      console.error('Erro na verificação de telefone:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Atualiza perfil
   */
  const updateProfile = async (data: Partial<AuthUser>) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await authService.updateProfile(data as UpdateProfileData);

      if (result.success && result.user) {
        dispatch({ type: 'SET_USER', payload: result.user });
      } else {
        dispatch({ type: 'SET_ERROR', payload: result.error || 'Erro ao atualizar perfil' });
      }
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Erro interno do servidor' });
    }
  };

  /**
   * Atualiza token
   */
  const refreshToken = async () => {
    try {
      const result = await authService.getCurrentUser();
      
      if (result.success && result.user) {
        dispatch({ type: 'SET_USER', payload: result.user });
      }
    } catch (error) {
      console.error('Erro ao atualizar token:', error);
    }
  };

  /**
   * Limpa erro
   */
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    verifyEmail,
    verifyPhone,
    updateProfile,
    refreshToken,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook para usar o contexto
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
}
