import { api } from './api';
import { 
  storeAccessToken, 
  storeRefreshToken, 
  storeUserData, 
  clearAuthData,
  storeLastEmail,
  storeRememberMe
} from './storage';
import { 
  LoginCredentials, 
  RegisterData, 
  AuthUser, 
  ForgotPasswordData,
  ResetPasswordData,
  VerificationCodeData
} from '../types/auth';
import { UpdateProfileData, DocumentUploadData } from '../types/user';

/**
 * Serviços de autenticação
 */
export class AuthService {
  /**
   * Realiza login do usuário
   */
  async login(credentials: LoginCredentials): Promise<{
    success: boolean;
    user?: AuthUser;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/login', {
        email: credentials.email,
        password: credentials.password,
      }, false);

      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data;

        // Armazena tokens e dados do usuário
        await Promise.all([
          storeAccessToken(accessToken),
          storeRefreshToken(refreshToken),
          storeUserData(user),
          storeLastEmail(credentials.email),
          storeRememberMe(credentials.rememberMe || false),
        ]);

        return {
          success: true,
          user
        };
      }

      return {
        success: false,
        error: response.error || 'Erro ao fazer login'
      };
    } catch (error) {
      console.error('Erro no login:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Registra novo usuário
   */
  async register(data: RegisterData): Promise<{
    success: boolean;
    user?: AuthUser;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/register', {
        email: data.email,
        password: data.password,
        fullName: data.fullName,
        phone: data.phone,
        userType: data.userType,
      }, false);

      if (response.success && response.data) {
        const { user, accessToken, refreshToken } = response.data;

        // Armazena tokens e dados do usuário
        await Promise.all([
          storeAccessToken(accessToken),
          storeRefreshToken(refreshToken),
          storeUserData(user),
          storeLastEmail(data.email),
        ]);

        return {
          success: true,
          user
        };
      }

      return {
        success: false,
        error: response.error || 'Erro ao criar conta'
      };
    } catch (error) {
      console.error('Erro no registro:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Faz logout do usuário
   */
  async logout(): Promise<void> {
    try {
      // Tenta invalidar o token no servidor
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Erro ao fazer logout no servidor:', error);
    } finally {
      // Sempre limpa os dados locais
      await clearAuthData();
    }
  }

  /**
   * Solicita recuperação de senha
   */
  async forgotPassword(data: ForgotPasswordData): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/forgot-password', {
        email: data.email,
      }, false);

      return {
        success: response.success,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao solicitar recuperação de senha:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Redefine senha com token
   */
  async resetPassword(data: ResetPasswordData): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/reset-password', {
        token: data.token,
        newPassword: data.newPassword,
      }, false);

      return {
        success: response.success,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Verifica email com código
   */
  async verifyEmail(data: VerificationCodeData): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/verify-email', {
        code: data.code,
      });

      return {
        success: response.success,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao verificar email:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Verifica telefone com código
   */
  async verifyPhone(data: VerificationCodeData): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const response = await api.post('/auth/verify-phone', {
        code: data.code,
      });

      return {
        success: response.success,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao verificar telefone:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Atualiza perfil do usuário
   */
  async updateProfile(data: UpdateProfileData): Promise<{
    success: boolean;
    user?: AuthUser;
    error?: string;
  }> {
    try {
      const response = await api.put('/user/profile', data);

      if (response.success && response.data) {
        // Atualiza dados locais
        await storeUserData(response.data);

        return {
          success: true,
          user: response.data
        };
      }

      return {
        success: false,
        error: response.error || 'Erro ao atualizar perfil'
      };
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Faz upload de documento
   */
  async uploadDocument(data: DocumentUploadData): Promise<{
    success: boolean;
    documentId?: string;
    error?: string;
  }> {
    try {
      const response = await api.upload('/user/documents', data.file, {
        type: data.type,
      });

      return {
        success: response.success,
        documentId: response.data?.id,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao fazer upload de documento:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Obtém perfil do usuário atual
   */
  async getCurrentUser(): Promise<{
    success: boolean;
    user?: AuthUser;
    error?: string;
  }> {
    try {
      const response = await api.get('/user/profile');

      if (response.success && response.data) {
        // Atualiza dados locais
        await storeUserData(response.data);

        return {
          success: true,
          user: response.data
        };
      }

      return {
        success: false,
        error: response.error || 'Erro ao obter perfil'
      };
    } catch (error) {
      console.error('Erro ao obter perfil:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Reenvia código de verificação
   */
  async resendVerificationCode(type: 'email' | 'phone'): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const response = await api.post(`/auth/resend-verification`, {
        type,
      });

      return {
        success: response.success,
        error: response.error
      };
    } catch (error) {
      console.error('Erro ao reenviar código:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }
}

// Instância singleton do serviço de autenticação
export const authService = new AuthService();
