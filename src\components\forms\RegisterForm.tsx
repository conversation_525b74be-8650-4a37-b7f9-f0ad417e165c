import { Ionicons } from '@expo/vector-icons';
import { yupResolver } from '@hookform/resolvers/yup';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import * as yup from 'yup';

import { useAuth } from '../../contexts/AuthContext';
import { colors } from '../../styles/colors';
import { RegisterData, UserType } from '../../types/auth';
import {
    formatPhone,
    validateEmail,
    validatePassword,
    validatePhone
} from '../../utils/validation';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

// Schema de validação
const registerSchema = yup.object().shape({
  fullName: yup
    .string()
    .required('Nome completo é obrigatório')
    .min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: yup
    .string()
    .required('Email é obrigatório')
    .test('email', 'Email inválido', (value) => {
      return value ? validateEmail(value) : false;
    }),
  phone: yup
    .string()
    .required('Telefone é obrigatório')
    .test('phone', 'Telefone inválido', (value) => {
      return value ? validatePhone(value) : false;
    }),
  password: yup
    .string()
    .required('Senha é obrigatória')
    .test('password', 'Senha não atende aos critérios', (value) => {
      if (!value) return false;
      const validation = validatePassword(value);
      return validation.isValid;
    }),
  confirmPassword: yup
    .string()
    .required('Confirmação de senha é obrigatória')
    .oneOf([yup.ref('password')], 'Senhas não coincidem'),
  userType: yup
    .string()
    .required('Tipo de usuário é obrigatório')
    .oneOf(['locador', 'locatario', 'ambos'], 'Tipo de usuário inválido'),
  acceptTerms: yup
    .boolean()
    .oneOf([true], 'Você deve aceitar os termos de uso'),
});

interface RegisterFormProps {
  onLogin: () => void;
  onDocumentVerification: () => void;
}

const USER_TYPE_OPTIONS: { value: UserType; label: string; description: string }[] = [
  {
    value: 'locador',
    label: 'Locador',
    description: 'Quero alugar meus imóveis'
  },
  {
    value: 'locatario',
    label: 'Locatário',
    description: 'Quero alugar um imóvel'
  },
  {
    value: 'ambos',
    label: 'Ambos',
    description: 'Quero alugar e ser locatário'
  },
];

export function RegisterForm({ onLogin, onDocumentVerification }: RegisterFormProps) {
  const { register, isLoading, error } = useAuth();
  const [selectedUserType, setSelectedUserType] = useState<UserType | null>(null);
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong'>('weak');

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<RegisterData>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      userType: 'locatario',
      acceptTerms: false,
    },
  });

  const watchPassword = watch('password');

  // Atualiza força da senha em tempo real
  React.useEffect(() => {
    if (watchPassword) {
      const validation = validatePassword(watchPassword);
      setPasswordStrength(validation.strength);
    }
  }, [watchPassword]);

  const onSubmit = async (data: RegisterData) => {
    try {
      await register(data);
      // Após registro bem-sucedido, redireciona para verificação de documentos
      onDocumentVerification();
    } catch (error) {
      console.error('Erro no registro:', error);
    }
  };

  const handleUserTypeSelect = (userType: UserType) => {
    setSelectedUserType(userType);
    setValue('userType', userType);
  };

  const getPasswordStrengthColor = () => {
    switch (passwordStrength) {
      case 'weak':
        return colors.red[300];
      case 'medium':
        return colors.yellow[300];
      case 'strong':
        return colors.green[300];
      default:
        return colors.gray[300];
    }
  };

  const getPasswordStrengthText = () => {
    switch (passwordStrength) {
      case 'weak':
        return 'Fraca';
      case 'medium':
        return 'Média';
      case 'strong':
        return 'Forte';
      default:
        return '';
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Criar Conta</Text>
        <Text style={styles.subtitle}>
          Preencha os dados para começar
        </Text>
      </View>

      <View style={styles.form}>
        <Controller
          control={control}
          name="fullName"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Nome Completo"
              placeholder="Seu nome completo"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.fullName?.message}
              leftIcon="person"
              autoCapitalize="words"
              autoComplete="name"
            />
          )}
        />

        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Email"
              placeholder="<EMAIL>"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.email?.message}
              leftIcon="mail"
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />
          )}
        />

        <Controller
          control={control}
          name="phone"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Telefone"
              placeholder="(11) 99999-9999"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.phone?.message}
              leftIcon="call"
              keyboardType="phone-pad"
              mask={formatPhone}
              autoComplete="tel"
            />
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, onBlur, value } }) => (
            <View>
              <Input
                label="Senha"
                placeholder="Sua senha"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={errors.password?.message}
                leftIcon="lock-closed"
                secureTextEntry
                autoComplete="new-password"
              />
              {value && (
                <View style={styles.passwordStrength}>
                  <View style={styles.strengthIndicator}>
                    <View
                      style={[
                        styles.strengthBar,
                        { backgroundColor: getPasswordStrengthColor() }
                      ]}
                    />
                  </View>
                  <Text style={[styles.strengthText, { color: getPasswordStrengthColor() }]}>
                    {getPasswordStrengthText()}
                  </Text>
                </View>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="confirmPassword"
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              label="Confirmar Senha"
              placeholder="Confirme sua senha"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={errors.confirmPassword?.message}
              leftIcon="lock-closed"
              secureTextEntry
              autoComplete="new-password"
            />
          )}
        />

        <View style={styles.userTypeSection}>
          <Text style={styles.userTypeLabel}>Tipo de Usuário</Text>
          <Text style={styles.userTypeDescription}>
            Selecione como você pretende usar a plataforma
          </Text>

          {USER_TYPE_OPTIONS.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.userTypeOption,
                selectedUserType === option.value && styles.userTypeOptionSelected
              ]}
              onPress={() => handleUserTypeSelect(option.value)}
            >
              <View style={styles.userTypeContent}>
                <View style={styles.userTypeHeader}>
                  <Text style={[
                    styles.userTypeTitle,
                    selectedUserType === option.value && styles.userTypeTextSelected
                  ]}>
                    {option.label}
                  </Text>
                  <Ionicons
                    name={selectedUserType === option.value ? 'radio-button-on' : 'radio-button-off'}
                    size={20}
                    color={selectedUserType === option.value ? colors.sky[600] : colors.gray[400]}
                  />
                </View>
                <Text style={[
                  styles.userTypeDesc,
                  selectedUserType === option.value && styles.userTypeTextSelected
                ]}>
                  {option.description}
                </Text>
              </View>
            </TouchableOpacity>
          ))}

          {errors.userType && (
            <Text style={styles.errorText}>{errors.userType.message}</Text>
          )}
        </View>

        <Controller
          control={control}
          name="acceptTerms"
          render={({ field: { onChange, value } }) => (
            <TouchableOpacity
              style={styles.termsContainer}
              onPress={() => onChange(!value)}
            >
              <Ionicons
                name={value ? 'checkbox' : 'checkbox-outline'}
                size={20}
                color={value ? colors.sky[600] : colors.gray[400]}
              />
              <Text style={styles.termsText}>
                Aceito os{' '}
                <Text style={styles.termsLink}>termos de uso</Text>
                {' '}e{' '}
                <Text style={styles.termsLink}>política de privacidade</Text>
              </Text>
            </TouchableOpacity>
          )}
        />

        {errors.acceptTerms && (
          <Text style={styles.errorText}>{errors.acceptTerms.message}</Text>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        <Button
          title="Criar Conta"
          onPress={handleSubmit(onSubmit)}
          loading={isLoading}
          style={styles.registerButton}
        />
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>Já tem uma conta? </Text>
        <TouchableOpacity onPress={onLogin}>
          <Text style={styles.loginLink}>Entrar</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
  },

  header: {
    marginBottom: 32,
    alignItems: 'center',
  },

  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 8,
  },

  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },

  form: {
    marginBottom: 32,
  },

  passwordStrength: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 8,
  },

  strengthIndicator: {
    flex: 1,
    height: 4,
    backgroundColor: colors.gray[200],
    borderRadius: 2,
    marginRight: 8,
  },

  strengthBar: {
    height: '100%',
    borderRadius: 2,
    width: '100%',
  },

  strengthText: {
    fontSize: 12,
    fontWeight: '600',
  },

  userTypeSection: {
    marginBottom: 24,
  },

  userTypeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 4,
  },

  userTypeDescription: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 12,
  },

  userTypeOption: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    backgroundColor: colors.gray[50],
  },

  userTypeOptionSelected: {
    borderColor: colors.sky[600],
    backgroundColor: colors.sky[50],
  },

  userTypeContent: {
    flex: 1,
  },

  userTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  userTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },

  userTypeDesc: {
    fontSize: 14,
    color: colors.gray[600],
  },

  userTypeTextSelected: {
    color: colors.sky[700],
  },

  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },

  termsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
  },

  termsLink: {
    color: colors.sky[600],
    fontWeight: '600',
  },

  errorContainer: {
    backgroundColor: colors.red[50],
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },

  errorText: {
    color: colors.red[300],
    fontSize: 12,
    marginTop: 4,
  },

  registerButton: {
    marginTop: 8,
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 24,
  },

  footerText: {
    fontSize: 14,
    color: colors.gray[600],
  },

  loginLink: {
    fontSize: 14,
    color: colors.sky[600],
    fontWeight: '600',
  },
});
