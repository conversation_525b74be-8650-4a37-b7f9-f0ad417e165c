import * as LocalAuthentication from 'expo-local-authentication';
import { BiometricAuthResult } from '../types/auth';

/**
 * Verifica se o dispositivo suporta autenticação biométrica
 */
export async function isBiometricSupported(): Promise<boolean> {
  try {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    return hasHardware && isEnrolled;
  } catch (error) {
    console.error('Erro ao verificar suporte biométrico:', error);
    return false;
  }
}

/**
 * Obtém os tipos de autenticação biométrica disponíveis
 */
export async function getBiometricTypes(): Promise<LocalAuthentication.AuthenticationType[]> {
  try {
    return await LocalAuthentication.supportedAuthenticationTypesAsync();
  } catch (error) {
    console.error('Erro ao obter tipos biométricos:', error);
    return [];
  }
}

/**
 * Realiza autenticação biométrica
 */
export async function authenticateWithBiometric(
  promptMessage: string = 'Autentique-se para continuar'
): Promise<BiometricAuthResult> {
  try {
    // Verifica se o dispositivo suporta biometria
    const isSupported = await isBiometricSupported();
    if (!isSupported) {
      return {
        success: false,
        error: 'Autenticação biométrica não disponível neste dispositivo',
        biometryType: 'none'
      };
    }

    // Obtém os tipos disponíveis
    const biometricTypes = await getBiometricTypes();
    const biometryType = getBiometryTypeString(biometricTypes);

    // Realiza a autenticação
    const result = await LocalAuthentication.authenticateAsync({
      promptMessage,
      cancelLabel: 'Cancelar',
      fallbackLabel: 'Usar senha',
      disableDeviceFallback: false,
    });

    if (result.success) {
      return {
        success: true,
        biometryType
      };
    } else {
      return {
        success: false,
        error: result.error || 'Autenticação biométrica falhou',
        biometryType
      };
    }
  } catch (error) {
    console.error('Erro na autenticação biométrica:', error);
    return {
      success: false,
      error: 'Erro interno na autenticação biométrica'
    };
  }
}

/**
 * Converte tipos de autenticação para string
 */
function getBiometryTypeString(
  types: LocalAuthentication.AuthenticationType[]
): 'fingerprint' | 'faceId' | 'iris' | 'none' {
  if (types.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
    return 'faceId';
  }
  if (types.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
    return 'fingerprint';
  }
  if (types.includes(LocalAuthentication.AuthenticationType.IRIS)) {
    return 'iris';
  }
  return 'none';
}

/**
 * Obtém o nome amigável do tipo de biometria
 */
export function getBiometryDisplayName(type: string): string {
  switch (type) {
    case 'fingerprint':
      return 'Digital';
    case 'faceId':
      return 'Face ID';
    case 'iris':
      return 'Íris';
    default:
      return 'Biometria';
  }
}

/**
 * Verifica se a autenticação biométrica está habilitada nas configurações do usuário
 */
export async function isBiometricEnabledInSettings(): Promise<boolean> {
  try {
    // Esta função seria implementada junto com o sistema de preferências do usuário
    // Por enquanto, retorna true se o dispositivo suporta
    return await isBiometricSupported();
  } catch (error) {
    console.error('Erro ao verificar configurações biométricas:', error);
    return false;
  }
}

/**
 * Solicita permissão para usar autenticação biométrica
 */
export async function requestBiometricPermission(): Promise<boolean> {
  try {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    if (!hasHardware) {
      return false;
    }

    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    if (!isEnrolled) {
      // Usuário não tem biometria configurada no dispositivo
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao solicitar permissão biométrica:', error);
    return false;
  }
}
