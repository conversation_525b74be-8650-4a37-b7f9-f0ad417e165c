# SaaS App - Sistema de Autenticação e Cadastro

Este é um aplicativo React Native com Expo que implementa um sistema completo de autenticação e cadastro com as seguintes funcionalidades:

## 🚀 Funcionalidades Implementadas

### RF001 - Sistema de Autenticação e Cadastro

✅ **Sistema de tipos de usuário (Locador/Locatário/Ambos)**
- Cadastro flexível permitindo que usuários sejam locadores, locatários ou ambos
- Interface intuitiva para seleção do tipo de usuário

✅ **Validação de documentos (CPF, RG, comprovante de residência)**
- Upload de documentos com validação de formato e tamanho
- Suporte a câmera, galeria e seleção de arquivos
- Validação de CPF e RG com algoritmos brasileiros
- Interface para verificação de documentos pós-cadastro

✅ **Login por email/senha e autenticação biométrica**
- Sistema de login tradicional com validação
- Suporte a autenticação biométrica (Face ID, Touch ID, Digital)
- Opção "Lembrar de mim" para conveniência

✅ **Recuperação de senha via email/SMS**
- Fluxo completo de recuperação de senha
- Interface para redefinição com validação de força da senha

✅ **Perfil com foto, dados pessoais e histórico**
- Tela de perfil completa com informações do usuário
- Status de verificação de email, telefone e documentos
- Configurações de segurança e privacidade

## 🛠️ Tecnologias Utilizadas

- **React Native** com **Expo 53**
- **Expo Router** para navegação
- **TypeScript** para tipagem
- **React Hook Form** + **Yup** para formulários e validação
- **Expo Local Authentication** para biometria
- **Expo Image Picker** para upload de documentos
- **AsyncStorage** e **SecureStore** para armazenamento
- **Context API** para gerenciamento de estado

## 📱 Estrutura do App

```
src/
├── app/                    # Telas (Expo Router)
│   ├── (auth)/            # Fluxo de autenticação
│   │   ├── login.tsx
│   │   ├── register.tsx
│   │   ├── forgot-password.tsx
│   │   └── document-verification.tsx
│   ├── (tabs)/            # Telas principais
│   │   ├── index.tsx      # Home
│   │   ├── profile.tsx    # Perfil
│   │   └── ...
│   ├── _layout.tsx        # Layout raiz
│   └── index.tsx          # Tela inicial
├── components/            # Componentes reutilizáveis
│   ├── forms/            # Formulários
│   └── ui/               # Componentes de UI
├── contexts/             # Contextos React
├── hooks/                # Hooks customizados
├── services/             # Serviços (API, Auth, Storage)
├── types/                # Definições TypeScript
├── utils/                # Utilitários e validações
└── styles/               # Estilos e cores
```

## 🚀 Como executar

1. **Instalar dependências**
   ```bash
   npm install
   ```

2. **Configurar variáveis de ambiente**
   ```bash
   cp .env.example .env
   # Edite o arquivo .env com suas configurações
   ```

3. **Iniciar o app**
   ```bash
   npx expo start
   ```

4. **Executar em dispositivo/emulador**
   - Escaneie o QR code com o Expo Go
   - Ou use `npx expo run:ios` / `npx expo run:android`

## 🔧 Configuração

### Variáveis de Ambiente

Crie um arquivo `.env` baseado no `.env.example`:

```env
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_ENV=development
```

### Permissões

O app solicita as seguintes permissões:
- **Câmera**: Para captura de documentos
- **Galeria**: Para seleção de imagens
- **Biometria**: Para autenticação segura

## 📋 Fluxo de Uso

1. **Primeiro acesso**: Usuário é direcionado para tela de login
2. **Cadastro**: Formulário completo com validações
3. **Verificação de documentos**: Upload opcional de CPF, RG e comprovante
4. **Acesso ao app**: Telas principais com perfil e configurações
5. **Autenticação biométrica**: Configurável nas preferências

## 🔐 Segurança

- Tokens armazenados de forma segura com SecureStore
- Validações client-side e server-side
- Criptografia de dados sensíveis
- Autenticação biométrica opcional
- Validação de força de senha

## 🎨 Design System

- Paleta de cores consistente
- Componentes reutilizáveis (Button, Input, Loading)
- Interface responsiva e acessível
- Feedback visual para estados de loading e erro

## 📚 Próximos Passos

- [ ] Integração com backend real
- [ ] Implementação de push notifications
- [ ] Sistema de chat/mensagens
- [ ] Funcionalidades de busca e listagem de imóveis
- [ ] Sistema de avaliações e reviews
- [ ] Integração com mapas
- [ ] Pagamentos integrados

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.
