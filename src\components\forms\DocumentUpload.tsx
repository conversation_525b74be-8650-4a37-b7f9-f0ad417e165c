import { Ionicons } from '@expo/vector-icons';
import { useState } from 'react';
import {
    ActionSheetIOS,
    Alert,
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { authService } from '../../services/auth';
import { colors } from '../../styles/colors';
import { DOCUMENT_REQUIREMENTS, DocumentType } from '../../types/document';
import {
    captureDocumentWithCamera,
    selectDocument,
    selectDocumentFromGallery,
    validateDocumentFile,
} from '../../utils/document';
import { Button } from '../ui/Button';

interface DocumentUploadProps {
  documentType: DocumentType;
  onUploadSuccess: (documentId: string) => void;
  onUploadError: (error: string) => void;
  onSkip?: () => void;
}

export function DocumentUpload({
  documentType,
  onUploadSuccess,
  onUploadError,
  onSkip,
}: DocumentUploadProps) {
  const [selectedFile, setSelectedFile] = useState<{
    uri: string;
    name: string;
    type: string;
  } | null>(null);
  const [uploading, setUploading] = useState(false);

  const requirement = DOCUMENT_REQUIREMENTS[documentType];

  const showImagePicker = () => {
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancelar', 'Câmera', 'Galeria', 'Arquivos'],
          cancelButtonIndex: 0,
        },
        (buttonIndex) => {
          switch (buttonIndex) {
            case 1:
              handleCameraCapture();
              break;
            case 2:
              handleGallerySelection();
              break;
            case 3:
              handleDocumentSelection();
              break;
          }
        }
      );
    } else {
      // Para Android, você pode usar um modal customizado ou biblioteca
      Alert.alert(
        'Selecionar Documento',
        'Escolha uma opção:',
        [
          { text: 'Cancelar', style: 'cancel' },
          { text: 'Câmera', onPress: handleCameraCapture },
          { text: 'Galeria', onPress: handleGallerySelection },
          { text: 'Arquivos', onPress: handleDocumentSelection },
        ]
      );
    }
  };

  const handleCameraCapture = async () => {
    try {
      console.log('DocumentUpload: Iniciando captura com câmera');
      const file = await captureDocumentWithCamera();
      console.log('DocumentUpload: Arquivo retornado da câmera:', file);

      if (file) {
        validateAndSetFile(file);
      } else {
        console.log('DocumentUpload: Nenhum arquivo retornado da câmera');
      }
    } catch (error) {
      console.error('DocumentUpload: Erro ao capturar com câmera:', error);
      Alert.alert('Erro', 'Não foi possível acessar a câmera. Verifique as permissões do aplicativo.');
    }
  };

  const handleGallerySelection = async () => {
    try {
      console.log('DocumentUpload: Iniciando seleção da galeria');
      const file = await selectDocumentFromGallery();
      console.log('DocumentUpload: Arquivo retornado da galeria:', file);

      if (file) {
        validateAndSetFile(file);
      } else {
        console.log('DocumentUpload: Nenhum arquivo retornado da galeria');
      }
    } catch (error) {
      console.error('DocumentUpload: Erro ao selecionar da galeria:', error);
      Alert.alert('Erro', 'Não foi possível acessar a galeria. Verifique as permissões do aplicativo.');
    }
  };

  const handleDocumentSelection = async () => {
    try {
      console.log('DocumentUpload: Iniciando seleção de documento');
      const file = await selectDocument();
      console.log('DocumentUpload: Arquivo retornado do seletor:', file);

      if (file) {
        validateAndSetFile(file);
      } else {
        console.log('DocumentUpload: Nenhum arquivo retornado do seletor');
      }
    } catch (error) {
      console.error('DocumentUpload: Erro ao selecionar documento:', error);
      Alert.alert('Erro', 'Não foi possível selecionar o arquivo. Tente novamente.');
    }
  };

  const validateAndSetFile = (file: {
    uri: string;
    name: string;
    type: string;
    size?: number;
  }) => {
    console.log('DocumentUpload: Validando arquivo:', file);

    const validation = validateDocumentFile(file, requirement);
    console.log('DocumentUpload: Resultado da validação:', validation);

    if (!validation.isValid) {
      Alert.alert(
        'Arquivo Inválido',
        validation.errors.join('\n') + '\n\nDeseja continuar mesmo assim?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Continuar',
            onPress: () => {
              console.log('DocumentUpload: Usuário optou por continuar com arquivo inválido');
              setSelectedFile(file);
            }
          }
        ]
      );
      return;
    }

    console.log('DocumentUpload: Arquivo válido, definindo como selecionado');
    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      Alert.alert('Erro', 'Selecione um arquivo primeiro');
      return;
    }

    setUploading(true);

    try {
      const result = await authService.uploadDocument({
        type: documentType,
        file: selectedFile,
      });

      if (result.success && result.documentId) {
        onUploadSuccess(result.documentId);
        Alert.alert('Sucesso', 'Documento enviado com sucesso!');
      } else {
        onUploadError(result.error || 'Erro ao enviar documento');
        Alert.alert('Erro', result.error || 'Erro ao enviar documento');
      }
    } catch (error) {
      console.error('Erro no upload:', error);
      onUploadError('Erro interno do servidor');
      Alert.alert('Erro', 'Erro interno do servidor');
    } finally {
      setUploading(false);
    }
  };

  const removeFile = () => {
    setSelectedFile(null);
  };

  const isImage = selectedFile?.type.startsWith('image/');

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{requirement.name}</Text>
        <Text style={styles.description}>{requirement.description}</Text>
        {requirement.required && (
          <Text style={styles.required}>* Obrigatório</Text>
        )}
      </View>

      <View style={styles.requirements}>
        <Text style={styles.requirementsTitle}>Requisitos:</Text>
        <Text style={styles.requirementsText}>
          • Formatos aceitos: {requirement.formats.join(', ')}
        </Text>
        <Text style={styles.requirementsText}>
          • Tamanho máximo: {requirement.maxSize}MB
        </Text>
        {requirement.examples && (
          <View style={styles.examples}>
            <Text style={styles.examplesTitle}>Exemplos:</Text>
            {requirement.examples.map((example, index) => (
              <Text key={index} style={styles.exampleText}>
                • {example}
              </Text>
            ))}
          </View>
        )}
      </View>

      {!selectedFile ? (
        <View>
          <TouchableOpacity style={styles.uploadArea} onPress={showImagePicker}>
            <Ionicons name="cloud-upload" size={48} color={colors.gray[400]} />
            <Text style={styles.uploadText}>Toque para selecionar arquivo</Text>
            <Text style={styles.uploadSubtext}>
              Câmera, galeria ou arquivos
            </Text>
          </TouchableOpacity>

          {onSkip && (
            <TouchableOpacity
              style={styles.skipButton}
              onPress={() => {
                console.log('DocumentUpload: Usuário optou por pular documento');
                onSkip();
              }}
            >
              <Text style={styles.skipButtonText}>
                Pular este documento por enquanto
              </Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View style={styles.filePreview}>
          <View style={styles.fileHeader}>
            <Text style={styles.fileName}>{selectedFile.name}</Text>
            <TouchableOpacity onPress={removeFile} style={styles.removeButton}>
              <Ionicons name="close" size={20} color={colors.red[300]} />
            </TouchableOpacity>
          </View>

          {isImage ? (
            <Image source={{ uri: selectedFile.uri }} style={styles.imagePreview} />
          ) : (
            <View style={styles.fileIcon}>
              <Ionicons name="document" size={48} color={colors.gray[400]} />
              <Text style={styles.fileType}>{selectedFile.type}</Text>
            </View>
          )}

          <Button
            title="Enviar Documento"
            onPress={handleUpload}
            loading={uploading}
            style={styles.uploadButton}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },

  header: {
    marginBottom: 16,
  },

  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 4,
  },

  description: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },

  required: {
    fontSize: 12,
    color: colors.red[300],
    fontWeight: '600',
  },

  requirements: {
    backgroundColor: colors.sky[50],
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },

  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 8,
  },

  requirementsText: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 4,
  },

  examples: {
    marginTop: 8,
  },

  examplesTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
    marginBottom: 4,
  },

  exampleText: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 2,
  },

  uploadArea: {
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    borderRadius: 8,
    padding: 32,
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },

  uploadText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
    marginTop: 12,
  },

  uploadSubtext: {
    fontSize: 14,
    color: colors.gray[500],
    marginTop: 4,
  },

  filePreview: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 16,
    backgroundColor: colors.gray[50],
  },

  fileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },

  fileName: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[900],
  },

  removeButton: {
    padding: 4,
  },

  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },

  fileIcon: {
    alignItems: 'center',
    padding: 24,
    marginBottom: 16,
  },

  fileType: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
  },

  uploadButton: {
    marginTop: 8,
  },

  skipButton: {
    marginTop: 12,
    padding: 12,
    alignItems: 'center',
  },

  skipButtonText: {
    fontSize: 14,
    color: colors.gray[500],
    textDecorationLine: 'underline',
  },
});
