import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

import { PageLoading } from '../components/ui/Loading';
import { useAuth } from '../contexts/AuthContext';
import { colors } from '../styles/colors';

export default function IndexScreen() {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // Usuário autenticado, redireciona para as tabs
        router.replace('/(tabs)');
      } else {
        // Usuário não autenticado, redireciona para login
        router.replace('/(auth)/login');
      }
    }
  }, [isAuthenticated, isLoading]);

  // Mostra loading enquanto verifica autenticação
  if (isLoading) {
    return (
      <View style={styles.container}>
        <StatusBar style="dark" />
        <PageLoading message="Verificando autenticação..." />
      </View>
    );
  }

  // Fallback - não deveria ser exibido normalmente
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <PageLoading message="Carregando..." />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
});
